{"data_mtime": 1751357282, "dep_lines": [7, 8, 9, 10, 11, 16, 17, 18, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["os", "sys", "json", "logging", "datetime", "ebm_generator", "llm_manager", "literature_search", "builtins", "_frozen_importlib", "_io", "_typeshed", "abc", "io", "json.decoder", "typing", "typing_extensions"], "hash": "0d207232ae28f8127b25edb43bac0af668ea6037", "id": "test_professional_ebm_fix", "ignore_all": false, "interface_hash": "8dfb65a88284c26bfb3ca197c5a2a17a4b449506", "mtime": 1751357293, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\xzyxgg\\test_professional_ebm_fix.py", "plugin_data": null, "size": 6628, "suppressed": [], "version_id": "1.15.0"}