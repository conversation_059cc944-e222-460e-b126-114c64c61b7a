{".class": "MypyFile", "_fullname": "extensions.visualization.data_visualizer", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "CURRENT_FONT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "extensions.visualization.data_visualizer.CURRENT_FONT", "name": "CURRENT_FONT", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "DataVisualizer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "extensions.visualization.data_visualizer.DataVisualizer", "name": "DataVisualizer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "extensions.visualization.data_visualizer.DataVisualizer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "extensions.visualization.data_visualizer", "mro": ["extensions.visualization.data_visualizer.DataVisualizer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "output_dir", "topic"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "extensions.visualization.data_visualizer.DataVisualizer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "output_dir", "topic"], "arg_types": ["extensions.visualization.data_visualizer.DataVisualizer", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DataVisualizer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_ensure_chinese_font": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "extensions.visualization.data_visualizer.DataVisualizer._ensure_chinese_font", "name": "_ensure_chinese_font", "type": null}}, "_generate_text_prisma_chart": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "search_stats"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "extensions.visualization.data_visualizer.DataVisualizer._generate_text_prisma_chart", "name": "_generate_text_prisma_chart", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "search_stats"], "arg_types": ["extensions.visualization.data_visualizer.DataVisualizer", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_text_prisma_chart of DataVisualizer", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_prepare_forest_plot_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "studies"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "extensions.visualization.data_visualizer.DataVisualizer._prepare_forest_plot_data", "name": "_prepare_forest_plot_data", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "studies"], "arg_types": ["extensions.visualization.data_visualizer.DataVisualizer", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_prepare_forest_plot_data of DataVisualizer", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_prepare_funnel_plot_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "studies"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "extensions.visualization.data_visualizer.DataVisualizer._prepare_funnel_plot_data", "name": "_prepare_funnel_plot_data", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "studies"], "arg_types": ["extensions.visualization.data_visualizer.DataVisualizer", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_prepare_funnel_plot_data of DataVisualizer", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_all_visualizations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "extensions.visualization.data_visualizer.DataVisualizer.generate_all_visualizations", "name": "generate_all_visualizations", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["extensions.visualization.data_visualizer.DataVisualizer", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_all_visualizations of DataVisualizer", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_evidence_quality_chart": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "quality_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "extensions.visualization.data_visualizer.DataVisualizer.generate_evidence_quality_chart", "name": "generate_evidence_quality_chart", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "quality_data"], "arg_types": ["extensions.visualization.data_visualizer.DataVisualizer", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_evidence_quality_chart of DataVisualizer", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_forest_plot": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "studies"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "extensions.visualization.data_visualizer.DataVisualizer.generate_forest_plot", "name": "generate_forest_plot", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "studies"], "arg_types": ["extensions.visualization.data_visualizer.DataVisualizer", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_forest_plot of DataVisualizer", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_funnel_plot": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "studies"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "extensions.visualization.data_visualizer.DataVisualizer.generate_funnel_plot", "name": "generate_funnel_plot", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "studies"], "arg_types": ["extensions.visualization.data_visualizer.DataVisualizer", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_funnel_plot of DataVisualizer", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_prisma_flow_chart": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "search_stats"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "extensions.visualization.data_visualizer.DataVisualizer.generate_prisma_flow_chart", "name": "generate_prisma_flow_chart", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "search_stats"], "arg_types": ["extensions.visualization.data_visualizer.DataVisualizer", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_prisma_flow_chart of DataVisualizer", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_risk_of_bias_summary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "bias_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "extensions.visualization.data_visualizer.DataVisualizer.generate_risk_of_bias_summary", "name": "generate_risk_of_bias_summary", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "bias_data"], "arg_types": ["extensions.visualization.data_visualizer.DataVisualizer", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_risk_of_bias_summary of DataVisualizer", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_study_characteristics_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "studies"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "extensions.visualization.data_visualizer.DataVisualizer.generate_study_characteristics_table", "name": "generate_study_characteristics_table", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "studies"], "arg_types": ["extensions.visualization.data_visualizer.DataVisualizer", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_study_characteristics_table of DataVisualizer", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "images_dir": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "extensions.visualization.data_visualizer.DataVisualizer.images_dir", "name": "images_dir", "type": "pathlib.Path"}}, "output_dir": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "extensions.visualization.data_visualizer.DataVisualizer.output_dir", "name": "output_dir", "type": "pathlib.Path"}}, "timestamp": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "extensions.visualization.data_visualizer.DataVisualizer.timestamp", "name": "timestamp", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, "type_of_any": 7}}}, "topic": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "extensions.visualization.data_visualizer.DataVisualizer.topic", "name": "topic", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "extensions.visualization.data_visualizer.DataVisualizer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "extensions.visualization.data_visualizer.DataVisualizer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "MATPLOTLIB_AVAILABLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "extensions.visualization.data_visualizer.MATPLOTLIB_AVAILABLE", "name": "MATPLOTLIB_AVAILABLE", "type": "builtins.bool"}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "extensions.visualization.data_visualizer.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "extensions.visualization.data_visualizer.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "extensions.visualization.data_visualizer.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "extensions.visualization.data_visualizer.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "extensions.visualization.data_visualizer.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "extensions.visualization.data_visualizer.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime", "kind": "Gdef"}, "e": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "extensions.visualization.data_visualizer.e", "name": "e", "type": {".class": "DeletedType", "source": "e"}}}, "fm": {".class": "SymbolTableNode", "cross_ref": "matplotlib.font_manager", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "extensions.visualization.data_visualizer.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "mpatches": {".class": "SymbolTableNode", "cross_ref": "matplotlib.patches", "kind": "Gdef"}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "pd": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "extensions.visualization.data_visualizer.pd", "name": "pd", "type": {".class": "AnyType", "missing_import_name": "extensions.visualization.data_visualizer.pd", "source_any": null, "type_of_any": 3}}}, "plt": {".class": "SymbolTableNode", "cross_ref": "matplotlib.pyplot", "kind": "Gdef"}, "setup_chinese_font": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "extensions.visualization.data_visualizer.setup_chinese_font", "name": "setup_chinese_font", "type": null}}, "sns": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "extensions.visualization.data_visualizer.sns", "name": "sns", "type": {".class": "AnyType", "missing_import_name": "extensions.visualization.data_visualizer.sns", "source_any": null, "type_of_any": 3}}}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "E:\\xzyxgg\\extensions\\visualization\\data_visualizer.py"}